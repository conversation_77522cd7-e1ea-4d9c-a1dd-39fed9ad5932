# VSCodeSandbox

🚀 **Complete VS Code isolation solution with global installation and project scaffolding**

VS Code Sandbox creates completely isolated VS Code environments that simulate fresh OS installations with zero shared state between profiles or interference with your host system. Now enhanced with global installation, self-update capabilities, and integrated project scaffolding.

## ✨ Features

- 🔒 **Complete Isolation**: Each profile runs in its own Linux namespace sandbox
- 🏠 **Fresh OS Simulation**: Every profile behaves like a brand new operating system
- 🚫 **Zero Interference**: No impact on existing VS Code installations
- 🔄 **Multiple Profiles**: Unlimited isolated environments that coexist peacefully
- 🌐 **Global Installation**: Install once, use anywhere on your system
- 🔄 **Self-Updating**: Built-in update mechanism to get latest features
- 📦 **Project Scaffolding**: Create project templates within isolated environments
- 🗂️ **Advanced Management**: Create, launch, remove, and monitor profiles
- 🧪 **Well Tested**: Comprehensive test suite ensures isolation effectiveness
- 🛡️ **Safe Operations**: Non-destructive with clean removal capabilities

## 🚀 Quick Start

### One-Line Installation (Recommended)
```bash
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash
```

### Manual Installation
```bash
# Download the installer
wget https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh
chmod +x install-vscode-sandbox.sh
sudo ./install-vscode-sandbox.sh
```

### 🎯 Recommended Workflow
```bash
# Create isolated VS Code profiles
vscode-sandbox client-work create      # Client project environment
vscode-sandbox personal-dev create     # Personal development environment
vscode-sandbox experimental create     # Safe testing environment

# Create projects within isolated profiles
vscode-sandbox client-work scaffold my-app --type react --git --vscode
vscode-sandbox personal-dev scaffold api-server --type node --git --docker

# Launch isolated environments
vscode-sandbox client-work launch
vscode-sandbox personal-dev launch
```

## 🎯 Use Cases

### **Development Project Isolation**
```bash
vscode-sandbox frontend-project create
vscode-sandbox backend-project create
vscode-sandbox mobile-app create
```

### **Client Work Separation**
```bash
vscode-sandbox client-alpha create
vscode-sandbox client-beta create
# Complete isolation between client projects
```

### **Technology Stack Environments**
```bash
vscode-sandbox python-ml create
vscode-sandbox nodejs-web create
vscode-sandbox rust-systems create
```

### **Project Scaffolding Within Isolated Environments**
```bash
# Create React project in isolated profile
vscode-sandbox frontend-project scaffold my-react-app --type react --git --vscode

# Create Node.js API in isolated profile
vscode-sandbox backend-project scaffold api-server --type node --git --docker

# Create Python project in isolated profile
vscode-sandbox python-ml scaffold ml-project --type python --git --vscode
```

## 🛠️ Enhanced VS Code Sandbox Tool

### `vscode-sandbox` - Unified Isolation Tool ⭐
**Complete VS Code isolation solution with global installation and project scaffolding**

```bash
vscode-sandbox <profile-name> [command] [options]
```

**Core Features:**
- ✅ Complete profile isolation (config, extensions, projects)
- ✅ Global installation to `/usr/local/bin`
- ✅ Self-update mechanism with `--update` flag
- ✅ Project scaffolding within isolated environments
- ✅ Advanced profile management and monitoring
- ✅ Works with any VS Code installation method

**Commands:**
- `create` - Create and launch isolated VS Code profile
- `launch` - Launch existing profile
- `scaffold` - Create project templates within profile
- `status` - Show detailed profile information
- `remove` - Remove profile completely
- `list` - List all profiles
- `--update` - Update to latest version

### Legacy Scripts (Still Available)
- **`vscode-isolate.sh`** - Advanced isolation engine with namespace support
- **`vscode-working-launcher.sh`** - Simple, reliable launcher
- **`vscode-profile-manager.sh`** - Advanced profile management utilities
- **`vscode-isolation-test.sh`** - Comprehensive test suite
- **`install.sh`** - Legacy installation script

## 📋 Requirements

- **Linux** with namespace support
- **util-linux** package (`unshare` command)
- **VS Code** installed (any method: snap, deb, AppImage, etc.)
- **Bash 4.0+** with standard utilities

## 🔧 Installation

### Enhanced VS Code Sandbox (Recommended)
```bash
# One-line global installation
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash

# Verify installation
vscode-sandbox --version
```

### Legacy Installation
```bash
# For legacy scripts
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install.sh | bash
```

### Manual Installation
```bash
<NAME_EMAIL>:MamunHoque/VSCodeSandbox.git
cd VSCodeSandbox
chmod +x *.sh
./vscode-isolation-test.sh  # Verify installation
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
./vscode-isolation-test.sh
```

Test the enhanced VS Code Sandbox:
```bash
# Test profile creation
vscode-sandbox test-profile create

# Test project scaffolding
vscode-sandbox test-profile scaffold test-app --type react --git

# Test profile management
vscode-sandbox list
vscode-sandbox test-profile status

# Clean up
vscode-sandbox test-profile remove
```

## 👨‍💻 Author

**Mamun Hoque**
- GitHub: [@MamunHoque](https://github.com/MamunHoque)
- Repository: [VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📝 What's New

### Version 3.0.0 - Enhanced Release
- ✅ **Unified VS Code Sandbox tool** - `vscode-sandbox` with global installation
- ✅ **Self-update mechanism** - Keep your tool current with `--update`
- ✅ **Project scaffolding integration** - Create projects within isolated profiles
- ✅ **Advanced profile management** - Status monitoring and comprehensive control
- ✅ **Global accessibility** - Run from anywhere with `/usr/local/bin` installation
- ✅ **Enhanced documentation** - Complete usage guides and examples

### Version 2.0.0 - Major Release
- ✅ **New recommended launcher** - `vscode-working-launcher.sh`
- ✅ **Complete rewrite** with enhanced isolation
- ✅ **Multiple launcher options** for different needs
- ✅ **Professional documentation** and troubleshooting
- ✅ **Comprehensive test suite** for reliability
- ✅ **One-command installation** with automatic setup

See [CHANGELOG.md](CHANGELOG.md) for complete details.

## 🔗 Links

- **Repository**: [https://github.com/MamunHoque/VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)
- [Changelog](CHANGELOG.md) - What's new and version history
- [Detailed Documentation](README-Enhanced-Isolation.md) - Technical details
- [Architecture Overview](docs/ARCHITECTURE.md) - How it works
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md) - Common issues

---

**VS Code Sandbox** - Because every project deserves its own universe. 🌌

*Created with ❤️ by [Mamun Hoque](https://github.com/MamunHoque)*
