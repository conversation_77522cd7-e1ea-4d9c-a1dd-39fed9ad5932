# VSCodeSandbox

🚀 **Complete VS Code isolation solution with enterprise-grade security using Linux namespaces**

VS Code Sandbox creates completely isolated VS Code environments that simulate fresh OS installations with zero shared state between profiles or interference with your host system. Choose from basic isolation for everyday use, maximum security isolation with Linux namespaces for enterprise environments, or the modern unified tool with global installation and project scaffolding.

## ✨ Features

### 🛡️ **Three Levels of Isolation**
- 🔒 **Basic Isolation** (`vscode-working-launcher.sh`) - Simple, reliable, works everywhere
- 🛡️ **Maximum Security** (`vscode-isolate.sh`) - Enterprise-grade Linux namespace isolation
- 🚀 **Modern Unified** (`vscode-sandbox`) - Best of both worlds with global installation

### 🏗️ **Core Capabilities**
- 🏠 **Fresh OS Simulation**: Every profile behaves like a brand new operating system
- 🚫 **Zero Interference**: No impact on existing VS Code installations
- 🔄 **Multiple Profiles**: Unlimited isolated environments that coexist peacefully
- 🗂️ **Advanced Management**: Create, launch, remove, and monitor profiles
- 🧪 **Well Tested**: Comprehensive test suite ensures isolation effectiveness
- 🛡️ **Safe Operations**: Non-destructive with clean removal capabilities

### 🚀 **Enhanced Features** (vscode-sandbox)
- 🌐 **Global Installation**: Install once, use anywhere on your system
- 🔄 **Self-Updating**: Built-in update mechanism to get latest features
- 📦 **Project Scaffolding**: Create project templates within isolated environments

## 🚀 Quick Start

### 🎯 Choose Your Isolation Level

#### **Option 1: Modern Unified Tool (Recommended for Most Users)**
```bash
# One-line installation
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash

# Create isolated profiles with project scaffolding
vscode-sandbox client-work create
vscode-sandbox client-work scaffold my-app --type react --git --vscode
vscode-sandbox client-work launch
```

#### **Option 2: Maximum Security Isolation (Enterprise/Security-Critical)**
```bash
# Clone repository for advanced isolation
git clone https://github.com/MamunHoque/VSCodeSandbox.git
cd VSCodeSandbox
chmod +x *.sh

# Enable user namespaces (if needed)
echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone

# Create maximum security isolated profile
./vscode-isolate.sh secure-project create
```

#### **Option 3: Simple & Reliable (Quick Setup)**
```bash
# Clone repository
git clone https://github.com/MamunHoque/VSCodeSandbox.git
cd VSCodeSandbox
chmod +x *.sh

# Create basic isolated profile
./vscode-working-launcher.sh myproject
```

## 🔍 **Isolation Comparison: Choose Your Security Level**

| Feature | Basic (`working-launcher`) | Maximum (`vscode-isolate`) | Modern (`vscode-sandbox`) |
|---------|---------------------------|---------------------------|--------------------------|
| **Setup Complexity** | ⭐ Simple | ⭐⭐⭐ Advanced | ⭐⭐ Moderate |
| **Security Level** | ⭐⭐ Basic | ⭐⭐⭐⭐⭐ Maximum | ⭐⭐⭐⭐ High |
| **System Requirements** | ⭐⭐⭐⭐⭐ Minimal | ⭐⭐ Linux + Namespaces | ⭐⭐⭐ Linux |
| **Compatibility** | ⭐⭐⭐⭐⭐ Universal | ⭐⭐⭐ Linux-specific | ⭐⭐⭐⭐ Linux |
| **Features** | ⭐⭐ Basic | ⭐⭐⭐⭐ Advanced | ⭐⭐⭐⭐⭐ Complete |

### 🛡️ **Security Isolation Levels**

#### **Basic Isolation** (`vscode-working-launcher.sh`)
- ✅ **Extensions isolated** - Separate extension directories
- ✅ **Settings isolated** - Separate configuration files
- ✅ **Workspace isolated** - Separate workspace state
- ❌ **System environment shared** - Same HOME, temp files
- ❌ **Process namespace shared** - Visible to host system
- ❌ **Network namespace shared** - Same network access

**Best for:** Daily development, project separation, client work isolation

#### **Maximum Security** (`vscode-isolate.sh`)
- ✅ **Extensions isolated** - Separate extension directories
- ✅ **Settings isolated** - Separate configuration files
- ✅ **Workspace isolated** - Separate workspace state
- ✅ **System environment isolated** - Separate HOME, XDG directories
- ✅ **Process namespace isolated** - Separate PID namespace
- ✅ **Mount namespace isolated** - Separate filesystem view
- ✅ **IPC namespace isolated** - Separate inter-process communication
- ✅ **UTS namespace isolated** - Separate hostname/domain
- ✅ **Temporary files isolated** - Separate /tmp directory

**Best for:** Enterprise environments, security-critical projects, confidential work

#### **Modern Unified** (`vscode-sandbox`)
- ✅ **Extensions isolated** - Separate extension directories
- ✅ **Settings isolated** - Separate configuration files
- ✅ **Workspace isolated** - Separate workspace state
- ✅ **System environment isolated** - Separate directories
- ✅ **Project scaffolding** - Integrated project creation
- ✅ **Global installation** - System-wide accessibility
- ✅ **Self-updating** - Automatic updates

**Best for:** Modern development workflows, team standardization, comprehensive isolation

## 🎯 **When to Use Each Tool**

### 🚀 **Use `vscode-sandbox` (Modern Unified) When:**
- ✅ You want the best of both worlds (security + convenience)
- ✅ You need project scaffolding within isolated environments
- ✅ You want global installation and self-update capabilities
- ✅ You're setting up standardized development environments
- ✅ You need good isolation with modern features
- ✅ You want a single tool that does everything

**Perfect for:** Modern development teams, standardized workflows, comprehensive project management

### 🛡️ **Use `vscode-isolate.sh` (Maximum Security) When:**
- ✅ You're working with highly sensitive or confidential projects
- ✅ You need enterprise-grade security isolation
- ✅ You require complete process and environment separation
- ✅ You're in a security-critical environment
- ✅ You need to prevent any data leakage between projects
- ✅ You want desktop integration with isolated MIME types

**Perfect for:** Enterprise environments, government projects, security audits, confidential client work

### 📦 **Use `vscode-working-launcher.sh` (Basic) When:**
- ✅ You want something that "just works" everywhere
- ✅ You're on a system without namespace support
- ✅ You need quick project separation without complexity
- ✅ You're new to VS Code isolation
- ✅ You have limited system permissions
- ✅ You want minimal setup and maximum compatibility

**Perfect for:** Quick prototyping, learning environments, basic project separation, legacy systems

## 🔐 **Security Trade-offs**

### **Security vs Convenience Matrix**

| Requirement | Basic | Maximum | Modern |
|-------------|-------|---------|--------|
| **Quick Setup** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **Process Isolation** | ❌ | ✅ | ✅ |
| **Environment Isolation** | ❌ | ✅ | ✅ |
| **File System Isolation** | ❌ | ✅ | ⭐ |
| **Network Isolation** | ❌ | ✅ | ❌ |
| **Desktop Integration** | ❌ | ✅ | ⭐ |
| **Project Scaffolding** | ❌ | ❌ | ✅ |
| **Global Installation** | ❌ | ❌ | ✅ |
| **Self-Updating** | ❌ | ❌ | ✅ |

### **Compliance & Regulatory Considerations**

**For GDPR/Privacy Compliance:**
- Use `vscode-isolate.sh` for maximum data isolation
- Separate profiles prevent cross-contamination of personal data
- Complete environment isolation ensures no data leakage

**For Enterprise Security:**
- `vscode-isolate.sh` provides defense-in-depth isolation
- Process namespace isolation prevents information disclosure
- Mount namespace isolation controls file system access

**For Development Team Standards:**
- `vscode-sandbox` provides consistent environments
- Project scaffolding ensures standardized project structure
- Global installation simplifies team onboarding

## 🎯 Use Cases

### **Development Project Isolation**
```bash
vscode-sandbox frontend-project create
vscode-sandbox backend-project create
vscode-sandbox mobile-app create
```

### **Client Work Separation**
```bash
vscode-sandbox client-alpha create
vscode-sandbox client-beta create
# Complete isolation between client projects
```

### **Technology Stack Environments**
```bash
vscode-sandbox python-ml create
vscode-sandbox nodejs-web create
vscode-sandbox rust-systems create
```

### **Project Scaffolding Within Isolated Environments**
```bash
# Create React project in isolated profile
vscode-sandbox frontend-project scaffold my-react-app --type react --git --vscode

# Create Node.js API in isolated profile
vscode-sandbox backend-project scaffold api-server --type node --git --docker

# Create Python project in isolated profile
vscode-sandbox python-ml scaffold ml-project --type python --git --vscode
```

## 📖 **Comprehensive Usage Guide**

### 🛡️ **Maximum Security Isolation (`vscode-isolate.sh`)**

#### **Creating Maximum Security Profiles**
```bash
# Create enterprise-grade isolated profile
./vscode-isolate.sh enterprise-project create

# What happens during creation:
# ✅ Creates isolated directory structure with separate HOME
# ✅ Sets up Linux namespace isolation (mount, UTS, IPC, PID)
# ✅ Creates desktop integration and MIME type associations
# ✅ Installs launcher scripts with namespace support
# ✅ Configures isolated XDG directories and environment
# ✅ Auto-installs Augment extension in isolated environment
```

#### **Advanced Isolation Features**
```bash
# Launch with complete namespace isolation
./vscode-isolate.sh enterprise-project launch

# Check profile status and isolation details
./vscode-isolate.sh enterprise-project status

# List all maximum security profiles
./vscode-isolate.sh "" list

# Remove profile completely (including desktop integration)
./vscode-isolate.sh enterprise-project remove
```

#### **Understanding Namespace Isolation**
When you create a profile with `vscode-isolate.sh`, it creates:

**🏠 Isolated Home Environment:**
- Separate `$HOME` directory (`~/.vscode-isolated/profiles/<name>/home`)
- Isolated XDG directories (config, cache, data, state)
- Separate temporary directory (`/tmp` is isolated)
- Custom environment variables and PATH

**🔒 Linux Namespace Isolation:**
- **Mount Namespace**: Separate filesystem view, isolated bind mounts
- **UTS Namespace**: Separate hostname and domain name
- **IPC Namespace**: Isolated inter-process communication
- **PID Namespace**: Separate process ID space (VS Code can't see host processes)

**🖥️ Desktop Integration:**
- Custom desktop entry: "VS Code - ProfileName (Isolated)"
- MIME type associations for profile-specific URIs
- Launcher scripts with namespace support

#### **Security Benefits of Maximum Isolation**
```bash
# Inside the isolated environment:
echo $HOME                    # Points to isolated home directory
ps aux                        # Shows only isolated processes
ls /tmp                       # Shows only isolated temporary files
env | grep XDG               # Shows isolated XDG directories
```

### 🚀 **Modern Unified Tool (`vscode-sandbox`)**

#### **Profile Management with Project Scaffolding**
```bash
# Create modern isolated profile
vscode-sandbox modern-project create

# Create projects within isolated environment
vscode-sandbox modern-project scaffold my-react-app --type react --git --vscode
vscode-sandbox modern-project scaffold api-server --type node --git --docker

# Launch with all projects available
vscode-sandbox modern-project launch

# Monitor profile status
vscode-sandbox modern-project status

# Global tool management
vscode-sandbox --update      # Self-update
vscode-sandbox --version     # Version info
vscode-sandbox list         # List all profiles
```

### 📦 **Basic Isolation (`vscode-working-launcher.sh`)**

#### **Simple Profile Creation**
```bash
# Create basic isolated profile (auto-creates if doesn't exist)
./vscode-working-launcher.sh simple-project

# Launch existing profile
./vscode-working-launcher.sh simple-project

# Profiles are stored in ~/.vscode-isolated/profiles/
```

## 🛠️ **Complete Tool Suite**

### 🚀 **`vscode-sandbox` - Modern Unified Tool** ⭐
**Recommended for most users - combines security with convenience**

```bash
vscode-sandbox <profile-name> [command] [options]
```

**Key Features:**
- ✅ **High-level isolation** using Linux namespaces
- ✅ **Global installation** to `/usr/local/bin`
- ✅ **Self-update mechanism** with `--update` flag
- ✅ **Integrated project scaffolding** within isolated environments
- ✅ **Advanced profile management** and monitoring
- ✅ **Cross-platform compatibility** with any VS Code installation

**Commands:**
- `create` - Create and launch isolated VS Code profile
- `launch` - Launch existing profile
- `scaffold` - Create project templates within profile (React, Node.js, Python, Go, Static)
- `status` - Show detailed profile information
- `remove` - Remove profile completely
- `list` - List all profiles
- `--update` - Update to latest version

### 🛡️ **`vscode-isolate.sh` - Maximum Security Engine**
**Enterprise-grade isolation using Linux namespaces**

```bash
./vscode-isolate.sh <profile-name> [command]
```

**Security Features:**
- ✅ **Complete namespace isolation** (mount, UTS, IPC, PID)
- ✅ **Separate process space** - isolated from host system
- ✅ **Isolated environment** - separate HOME, XDG directories
- ✅ **Desktop integration** - custom MIME types and launchers
- ✅ **Filesystem isolation** - controlled mount points
- ✅ **Automatic extension installation** in isolated environment

**Commands:**
- `create` - Create maximum security isolated profile
- `launch` - Launch with full namespace isolation
- `status` - Show detailed isolation status
- `remove` - Complete removal including desktop integration
- `list` - List all maximum security profiles

### 📦 **`vscode-working-launcher.sh` - Simple & Reliable**
**Basic isolation that works everywhere**

```bash
./vscode-working-launcher.sh <profile-name>
```

**Simplicity Features:**
- ✅ **Universal compatibility** - works with any VS Code installation
- ✅ **No special requirements** - no namespaces needed
- ✅ **Auto-creation** - creates profiles automatically
- ✅ **Minimal setup** - just works out of the box
- ✅ **Basic isolation** - separate extensions and settings

### 🔧 **Supporting Tools**
- **`vscode-profile-manager.sh`** - Advanced profile management utilities
- **`vscode-isolation-test.sh`** - Comprehensive test suite for all isolation levels
- **`install.sh`** - Legacy installation script for basic tools
- **`install-vscode-sandbox.sh`** - Modern installation script for unified tool

## 📋 Requirements

- **Linux** with namespace support
- **util-linux** package (`unshare` command)
- **VS Code** installed (any method: snap, deb, AppImage, etc.)
- **Bash 4.0+** with standard utilities

## 🔧 Installation

### 🚀 **Modern Unified Tool (Recommended)**
```bash
# One-line global installation
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash

# Verify installation
vscode-sandbox --version
```

### 🛡️ **Maximum Security Isolation Setup**

#### **System Requirements for Maximum Isolation**
- **Linux Operating System** (Ubuntu 18.04+, Debian 10+, CentOS 8+, etc.)
- **util-linux package** (provides `unshare` command)
- **User namespaces enabled** (most modern distributions)
- **VS Code installed** (any method: snap, deb, AppImage, etc.)

#### **Step 1: Check System Compatibility**
```bash
# Check if unshare is available
which unshare
unshare --help

# Test user namespace support
unshare -U true && echo "✅ User namespaces supported" || echo "❌ User namespaces not available"

# Check VS Code installation
which code || echo "Please install VS Code first"
```

#### **Step 2: Enable User Namespaces (if needed)**
```bash
# Check current setting
cat /proc/sys/kernel/unprivileged_userns_clone

# Enable user namespaces (temporary)
echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone

# Enable permanently (add to /etc/sysctl.conf)
echo 'kernel.unprivileged_userns_clone = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### **Step 3: Install Maximum Security Tools**
```bash
# Clone repository
git clone https://github.com/MamunHoque/VSCodeSandbox.git
cd VSCodeSandbox

# Make scripts executable
chmod +x *.sh

# Test namespace support
./vscode-isolation-test.sh

# Create your first maximum security profile
./vscode-isolate.sh secure-project create
```

### 📦 **Package Installation (Alternative)**
```bash
# Install required packages on Ubuntu/Debian
sudo apt update
sudo apt install util-linux git curl

# Install required packages on CentOS/RHEL/Fedora
sudo dnf install util-linux git curl
# or
sudo yum install util-linux git curl
```

### 🔧 **Basic Installation (Simple Isolation)**
```bash
# For basic isolation without namespace requirements
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install.sh | bash
```

## 🚨 **Troubleshooting Maximum Security Isolation**

### **Common Namespace Issues**

#### **User Namespaces Not Available**
```bash
# Error: "Operation not permitted" when creating profiles
# Solution 1: Enable user namespaces
echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone

# Solution 2: Check kernel support
uname -r  # Kernel 3.8+ required for user namespaces

# Solution 3: Check distribution settings
# Some distributions disable user namespaces by default
grep CONFIG_USER_NS /boot/config-$(uname -r)
```

#### **Permission Denied Errors**
```bash
# Error: Cannot create namespace
# Check if your user can create namespaces
unshare -U whoami

# If fails, check system limits
cat /proc/sys/user/max_user_namespaces

# Increase limits if needed (temporary)
echo 10000 | sudo tee /proc/sys/user/max_user_namespaces
```

#### **VS Code Won't Launch in Isolated Environment**
```bash
# Error: VS Code fails to start in namespace
# Solution 1: Disable GPU sandbox (already included in script)
code --disable-gpu-sandbox --no-sandbox

# Solution 2: Check VS Code binary detection
./vscode-isolate.sh myproject create
# If fails, set custom binary path:
VSCODE_BINARY=/snap/bin/code ./vscode-isolate.sh myproject create
```

#### **Desktop Integration Issues**
```bash
# Error: Desktop entry not appearing
# Update desktop database manually
update-desktop-database ~/.local/share/applications

# Check MIME type registration
xdg-mime query default x-scheme-handler/vscode-myproject
```

### **Performance Considerations**
```bash
# Namespace isolation adds minimal overhead, but:
# - First launch may be slower (setting up namespaces)
# - Memory usage slightly higher (isolated processes)
# - File I/O performance unchanged

# Monitor resource usage
ps aux | grep code  # Check isolated processes
du -sh ~/.vscode-isolated/profiles/*/  # Check disk usage
```

### **Fallback Options**
```bash
# If maximum isolation doesn't work, use alternatives:

# Option 1: Modern unified tool (good isolation + features)
vscode-sandbox myproject create

# Option 2: Basic isolation (guaranteed to work)
./vscode-working-launcher.sh myproject

# Option 3: Disable specific namespace features
# Edit vscode-isolate.sh and remove problematic namespace flags
```

## 🧪 Testing

### **Test Maximum Security Isolation**
```bash
# Run comprehensive test suite
./vscode-isolation-test.sh

# Test namespace support specifically
unshare -U -m -i -p --fork echo "Namespaces working!"

# Create test profile with maximum isolation
./vscode-isolate.sh test-secure create
./vscode-isolate.sh test-secure status
./vscode-isolate.sh test-secure remove
```

### **Test Modern Unified Tool**
```bash
# Test profile creation
vscode-sandbox test-profile create

# Test project scaffolding
vscode-sandbox test-profile scaffold test-app --type react --git

# Test profile management
vscode-sandbox list
vscode-sandbox test-profile status

# Clean up
vscode-sandbox test-profile remove
```

### **Test Basic Isolation**
```bash
# Test simple launcher
./vscode-working-launcher.sh test-basic

# Verify isolation
ls ~/.vscode-isolated/profiles/test-basic/
```

## 👨‍💻 Author

**Mamun Hoque**
- GitHub: [@MamunHoque](https://github.com/MamunHoque)
- Repository: [VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📝 What's New

### Version 3.0.0 - Enhanced Security & Modern Tools
- 🚀 **Modern Unified Tool** - `vscode-sandbox` combines maximum isolation with ease of use
- 🛡️ **Enhanced Security Documentation** - Comprehensive guide for enterprise-grade isolation
- 🔒 **Maximum Security Mode** - `vscode-isolate.sh` with Linux namespace isolation
- 🌐 **Global Installation** - System-wide accessibility with self-update mechanism
- 📦 **Integrated Project Scaffolding** - Create projects within isolated environments
- 🗂️ **Advanced Profile Management** - Status monitoring and comprehensive control
- 📚 **Complete Documentation** - Detailed guides for all isolation levels

**🎯 Recommended Approach:** The new `vscode-sandbox` tool provides the perfect balance of maximum security isolation (using Linux namespaces like `vscode-isolate.sh`) with modern convenience features like global installation and project scaffolding.

### Version 2.0.0 - Major Release
- ✅ **Multiple isolation levels** - Basic, maximum security, and modern unified
- ✅ **Linux namespace support** - Enterprise-grade security isolation
- ✅ **Desktop integration** - MIME types and launcher scripts
- ✅ **Comprehensive test suite** - Ensures isolation effectiveness
- ✅ **Professional documentation** - Detailed troubleshooting guides
- ✅ **Cross-compatibility** - Works with any VS Code installation method

### Key Security Enhancements
- 🛡️ **Process isolation** - Separate PID namespaces prevent process visibility
- 🏠 **Environment isolation** - Completely separate HOME and XDG directories
- 🔒 **Mount isolation** - Isolated filesystem view with controlled access
- 🌐 **Network isolation** - Optional network namespace separation
- 📁 **Temporary file isolation** - Separate /tmp directories for each profile

See [CHANGELOG.md](CHANGELOG.md) for complete details.

## 🔗 Links

- **Repository**: [https://github.com/MamunHoque/VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)
- [Changelog](CHANGELOG.md) - What's new and version history
- [Detailed Documentation](README-Enhanced-Isolation.md) - Technical details
- [Architecture Overview](docs/ARCHITECTURE.md) - How it works
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md) - Common issues

---

**VS Code Sandbox** - Because every project deserves its own universe. 🌌

*Created with ❤️ by [Mamun Hoque](https://github.com/MamunHoque)*
