# Project Scaffold - Complete Usage Guide

This guide provides comprehensive examples and best practices for using Project Scaffold.

## 🚀 Getting Started

### Installation Verification
```bash
# Check if project-scaffold is installed
which project-scaffold

# Check version
project-scaffold --version

# List available project types
project-scaffold --list
```

## 📋 Project Type Examples

### Frontend Development

#### React Projects
```bash
# Basic React app
project-scaffold my-react-app react

# React with TypeScript, Git, and VS Code config
project-scaffold my-ts-app react-ts --git --vscode

# Production-ready React app with all features
project-scaffold production-app react --git --vscode --docker --github
```

#### Next.js Projects
```bash
# Basic Next.js app
project-scaffold my-nextjs-app nextjs

# Full-stack Next.js with database setup
project-scaffold fullstack-app nextjs --git --vscode --docker --github
```

#### Vue.js Projects
```bash
# Vue.js application
project-scaffold my-vue-app vue --git --vscode

# Vue with Docker for deployment
project-scaffold vue-prod vue --git --docker --github
```

### Backend Development

#### Node.js APIs
```bash
# Express.js API server
project-scaffold my-api express --git --vscode --docker

# Fastify API with GitHub Actions
project-scaffold fast-api fastify --git --github

# Node.js microservice
project-scaffold user-service node --git --vscode --docker --github
```

#### Python Projects
```bash
# Basic Python project
project-scaffold my-python-app python --git --vscode

# Django web application
project-scaffold my-website django --git --vscode --docker

# FastAPI microservice
project-scaffold api-service fastapi --git --vscode --docker --github

# Data science project
project-scaffold data-analysis python --git --vscode
```

### Systems Programming

#### Go Applications
```bash
# Go CLI application
project-scaffold my-cli go --git --vscode

# Go web service
project-scaffold web-service go --git --vscode --docker --github

# Go microservice with full CI/CD
project-scaffold user-service go --git --vscode --docker --github
```

#### Rust Projects
```bash
# Rust application
project-scaffold my-rust-app rust --git --vscode

# Rust with Docker deployment
project-scaffold rust-service rust --git --vscode --docker --github
```

### Enterprise Development

#### Java Projects
```bash
# Java Maven project
project-scaffold my-java-app java --git --vscode

# Spring Boot application
project-scaffold my-spring-app spring --git --vscode --docker --github
```

#### PHP Projects
```bash
# PHP project with Composer
project-scaffold my-php-app php --git --vscode

# Laravel application
project-scaffold my-laravel-app laravel --git --vscode --docker --github
```

## 🛠️ Configuration Options Explained

### Git Integration (`--git`)
When you use `--git`, Project Scaffold will:
- Initialize a Git repository
- Create an appropriate `.gitignore` file for your project type
- Make an initial commit with all generated files
- Set up the repository for immediate use

```bash
# Example: React project with Git
project-scaffold my-app react --git

# What happens:
# 1. Creates project structure
# 2. Initializes Git repository
# 3. Creates .gitignore with Node.js rules
# 4. Adds all files to Git
# 5. Makes initial commit
```

### VS Code Configuration (`--vscode`)
Adds comprehensive VS Code setup:
- **settings.json** - Editor configuration (tab size, formatting, etc.)
- **extensions.json** - Recommended extensions for your project type
- **launch.json** - Debug configuration for your language

```bash
# Example: Python project with VS Code config
project-scaffold data-project python --vscode

# Generated .vscode/ folder contains:
# ├── settings.json        # Editor settings
# ├── extensions.json      # Python, linting extensions
# └── launch.json          # Python debugging config
```

### Docker Support (`--docker`)
Creates production-ready Docker configuration:
- **Dockerfile** - Optimized for your project type
- **docker-compose.yml** - Development environment setup
- **.dockerignore** - Excludes unnecessary files

```bash
# Example: Node.js API with Docker
project-scaffold my-api express --docker

# Generated Docker files:
# ├── Dockerfile           # Multi-stage build
# ├── docker-compose.yml   # Dev environment
# └── .dockerignore        # Optimized excludes
```

### GitHub Actions (`--github`)
Sets up CI/CD pipeline:
- Automated testing on push/PR
- Multi-version testing (where applicable)
- Linting and code quality checks
- Build verification

```bash
# Example: Full CI/CD setup
project-scaffold my-service go --github

# Generated .github/workflows/ci.yml includes:
# - Go version matrix testing
# - Build verification
# - Test execution
# - Automated on push/PR
```

## 🔧 Advanced Usage Patterns

### Microservices Architecture
```bash
# Create multiple related services
project-scaffold user-service go --git --vscode --docker --github
project-scaffold auth-service go --git --vscode --docker --github
project-scaffold api-gateway node --git --vscode --docker --github
project-scaffold frontend react --git --vscode --docker --github
```

### Full-Stack Development
```bash
# Backend API
project-scaffold backend fastapi --git --vscode --docker --github

# Frontend application
project-scaffold frontend react-ts --git --vscode --docker --github

# Admin dashboard
project-scaffold admin vue --git --vscode --docker
```

### Data Science Workflow
```bash
# Data processing service
project-scaffold data-processor python --git --vscode --docker

# ML model API
project-scaffold ml-api fastapi --git --vscode --docker --github

# Data visualization dashboard
project-scaffold dashboard react --git --vscode
```

## 🚨 Common Issues and Solutions

### Permission Errors
```bash
# If you get permission errors
sudo project-scaffold --install

# Or use local installation
./project-scaffold my-app react --git
```

### Dependency Installation Issues
```bash
# Skip automatic dependency installation
project-scaffold my-app react --no-install

# Then manually install
cd my-app
npm install
```

### Overwriting Existing Projects
```bash
# Force overwrite existing directory
project-scaffold existing-project react --force

# This will remove the existing directory and create new
```

### Custom Templates
```bash
# Use custom Git template (future feature)
project-scaffold my-app custom --template https://github.com/user/template.git
```

## 📊 Project Structure Examples

### React Project Structure
```
my-react-app/
├── src/
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   ├── App.jsx
│   └── main.jsx
├── public/
├── .vscode/
├── .github/workflows/
├── Dockerfile
├── docker-compose.yml
├── .gitignore
└── package.json
```

### Python Project Structure
```
my-python-app/
├── src/
│   └── main.py
├── tests/
│   └── test_main.py
├── docs/
├── .vscode/
├── .github/workflows/
├── requirements.txt
├── setup.py
├── pytest.ini
├── Dockerfile
├── .env.example
└── .gitignore
```

### Go Project Structure
```
my-go-app/
├── cmd/
│   └── main.go
├── internal/
├── pkg/
├── .vscode/
├── .github/workflows/
├── Dockerfile
├── docker-compose.yml
├── go.mod
├── go.sum
└── .gitignore
```

## 🔄 Maintenance and Updates

### Keeping Project Scaffold Updated
```bash
# Check for updates regularly
project-scaffold --update

# Verify update
project-scaffold --version
```

### Updating Generated Projects
After updating Project Scaffold, you can:
1. Create a new project with the same name using `--force`
2. Compare the new structure with your existing project
3. Manually apply relevant improvements

### Backup Before Updates
```bash
# Backup current installation
sudo cp /usr/local/bin/project-scaffold /usr/local/bin/project-scaffold.backup

# Update
project-scaffold --update

# Restore if needed
sudo cp /usr/local/bin/project-scaffold.backup /usr/local/bin/project-scaffold
```

## 💡 Best Practices

### Project Naming
- Use lowercase with hyphens: `my-awesome-app`
- Avoid spaces and special characters
- Be descriptive but concise

### Option Selection
- Always use `--git` for version control
- Use `--vscode` if you use VS Code
- Use `--docker` for deployment-ready projects
- Use `--github` for open source or team projects

### Development Workflow
1. Create project with appropriate options
2. Navigate to project directory
3. Install dependencies (if not done automatically)
4. Start development server
5. Begin coding!

## 🎯 Tips and Tricks

### Quick Project Creation
```bash
# Create alias for common patterns
alias react-project='project-scaffold'
alias api-project='project-scaffold'

# Use them
react-project my-app react --git --vscode
api-project my-api express --git --vscode --docker
```

### Batch Project Creation
```bash
#!/bin/bash
# Create multiple related projects
projects=("frontend:react" "backend:express" "admin:vue")

for project in "${projects[@]}"; do
    name="${project%:*}"
    type="${project#*:}"
    project-scaffold "$name" "$type" --git --vscode --docker
done
```

### Environment-Specific Configurations
```bash
# Development environment
project-scaffold dev-app react --git --vscode

# Production environment
project-scaffold prod-app react --git --docker --github

# Testing environment
project-scaffold test-app react --git --no-install
```

---

This guide covers the most common usage patterns. For more specific examples or troubleshooting, check the main README or open an issue on GitHub.
