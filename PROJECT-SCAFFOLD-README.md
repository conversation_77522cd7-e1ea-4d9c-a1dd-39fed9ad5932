# Project Scaffold - Universal Project Creation Tool

🚀 **A comprehensive project scaffolding tool for multiple programming languages and frameworks**

Project Scaffold is a powerful command-line tool that helps you quickly create new projects with best practices, proper structure, and modern tooling configurations.

## ✨ Features

- 🚀 **Multi-Language Support** - React, Node.js, Python, Go, Rust, Java, PHP, Ruby, and more
- 📦 **Template System** - Pre-configured project templates with best practices
- 🔄 **Self-Updating** - Built-in update mechanism to get the latest features
- 🌐 **Global Installation** - Install once, use anywhere on your system
- 🛠️ **Customizable** - Add VS Code config, Docker, GitHub Actions, and more
- 📋 **Best Practices** - Follows industry standards and modern development practices

## 🚀 Quick Installation

### One-Line Installation (Recommended)
```bash
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-project-scaffold.sh | sudo bash
```

### Manual Installation
```bash
# Download the installer
wget https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-project-scaffold.sh

# Make it executable
chmod +x install-project-scaffold.sh

# Run the installer
sudo ./install-project-scaffold.sh
```

### Local Installation (No sudo required)
```bash
# Download the script
wget https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/project-scaffold

# Make it executable
chmod +x project-scaffold

# Use it locally
./project-scaffold my-app react --git --vscode
```

## 📋 Usage

### Basic Usage
```bash
project-scaffold <project-name> <project-type> [options]
```

### Quick Examples
```bash
# Create a React app with Git and VS Code config
project-scaffold my-react-app react --git --vscode

# Create an Express API with Docker and GitHub Actions
project-scaffold my-api express --docker --github

# Create a Python project with all features
project-scaffold ml-project python --git --vscode --docker --github

# Create a Next.js app
project-scaffold my-website nextjs --git --vscode

# List all available project types
project-scaffold --list

# Update to the latest version
project-scaffold --update
```

## 🎯 Supported Project Types

### Frontend Frameworks
- **react** - React application with Vite
- **react-ts** - React with TypeScript
- **nextjs** - Next.js application
- **vue** - Vue.js application
- **angular** - Angular application
- **static** - Static HTML/CSS/JS website

### Backend & APIs
- **node** - Node.js application
- **node-ts** - Node.js with TypeScript
- **express** - Express.js API server
- **fastify** - Fastify API server

### Python
- **python** - Python project with virtual environment
- **django** - Django web application
- **flask** - Flask web application
- **fastapi** - FastAPI application

### Systems & Compiled Languages
- **go** - Go application
- **rust** - Rust application
- **java** - Java Maven project
- **spring** - Spring Boot application

### Web Frameworks
- **php** - PHP project with Composer
- **laravel** - Laravel application
- **ruby** - Ruby project with Bundler
- **rails** - Ruby on Rails application

### Desktop & Mobile
- **electron** - Electron desktop application

## 🛠️ Available Options

### Configuration Options
- `--git` - Initialize Git repository with appropriate .gitignore
- `--vscode` - Add VS Code configuration (settings, extensions, launch config)
- `--docker` - Add Docker configuration (Dockerfile, docker-compose.yml)
- `--github` - Add GitHub Actions workflow for CI/CD
- `--force` - Overwrite existing directory
- `--no-install` - Skip dependency installation

### Commands
- `--help, -h` - Show help message
- `--version, -v` - Show version information
- `--list, -l` - List all available project types
- `--update` - Update to the latest version
- `--install` - Install globally to /usr/local/bin
- `--uninstall` - Remove global installation

## 📖 Detailed Examples

### React Development Setup
```bash
# Create a complete React development environment
project-scaffold my-react-app react --git --vscode --docker --github

# What you get:
# ├── src/                     # Source code
# ├── public/                  # Public assets
# ├── .vscode/                 # VS Code configuration
# │   ├── settings.json        # Editor settings
# │   ├── extensions.json      # Recommended extensions
# │   └── launch.json          # Debug configuration
# ├── .github/workflows/       # GitHub Actions
# │   └── ci.yml              # CI/CD pipeline
# ├── Dockerfile              # Docker configuration
# ├── docker-compose.yml      # Development environment
# ├── .gitignore              # Git ignore rules
# └── package.json            # Dependencies and scripts
```

### Python Data Science Project
```bash
# Create a Python project for data science
project-scaffold data-analysis python --git --vscode --docker

# What you get:
# ├── src/                     # Source code
# │   └── main.py             # Main application
# ├── tests/                   # Test files
# │   └── test_main.py        # Unit tests
# ├── docs/                    # Documentation
# ├── .vscode/                 # VS Code configuration
# ├── requirements.txt         # Python dependencies
# ├── setup.py                # Package configuration
# ├── pytest.ini             # Test configuration
# ├── Dockerfile              # Docker configuration
# └── .env.example            # Environment variables template
```

### Full-Stack Development
```bash
# Create backend API
project-scaffold my-api express --git --vscode --docker --github

# Create frontend app
project-scaffold my-frontend react --git --vscode --docker --github

# Both projects will have:
# - Git repositories with proper .gitignore
# - VS Code configuration with recommended extensions
# - Docker setup for containerization
# - GitHub Actions for CI/CD
```

## 🔄 Self-Update Feature

Project Scaffold includes a built-in self-update mechanism:

```bash
# Check current version
project-scaffold --version

# Update to the latest version
project-scaffold --update

# The update will:
# 1. Download the latest version from GitHub
# 2. Backup your current installation
# 3. Replace with the new version
# 4. Verify the installation
```

## 🌐 Global Installation Benefits

When installed globally, you can:

- Run `project-scaffold` from any directory
- Create projects anywhere on your system
- Use the self-update feature
- Access all features without path issues

## 🔧 System Requirements

### Minimum Requirements
- **Linux** operating system
- **Bash 4.0+**
- **curl** or **wget** for downloading
- **Git** (optional, for --git flag)

### Language-Specific Requirements
- **Node.js 16+** - For JavaScript/TypeScript projects
- **Python 3.8+** - For Python projects
- **Go 1.19+** - For Go projects
- **Rust 1.70+** - For Rust projects
- **Java 11+** & **Maven** - For Java projects
- **PHP 8.0+** - For PHP projects
- **Ruby 3.0+** - For Ruby projects

## 🚨 Troubleshooting

### Permission Issues
```bash
# If you get permission errors during installation
sudo project-scaffold --install

# Or reinstall globally
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-project-scaffold.sh | sudo bash
```

### Update Issues
```bash
# If update fails, try manual reinstallation
sudo project-scaffold --uninstall
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-project-scaffold.sh | sudo bash
```

### Command Not Found
```bash
# Check if /usr/local/bin is in your PATH
echo $PATH

# Add to PATH if missing (add to ~/.bashrc or ~/.zshrc)
export PATH="/usr/local/bin:$PATH"
```

## 📝 Contributing

We welcome contributions! Here's how you can help:

1. **Add New Project Types** - Implement support for new languages/frameworks
2. **Improve Templates** - Enhance existing project templates
3. **Fix Bugs** - Report and fix issues
4. **Documentation** - Improve documentation and examples

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Mamun Hoque**
- GitHub: [@MamunHoque](https://github.com/MamunHoque)
- Repository: [VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)

## 🔗 Links

- **Repository**: [https://github.com/MamunHoque/VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)
- **Issues**: [Report bugs or request features](https://github.com/MamunHoque/VSCodeSandbox/issues)
- **Discussions**: [Community discussions](https://github.com/MamunHoque/VSCodeSandbox/discussions)

---

**Project Scaffold** - Because every great project starts with a solid foundation. 🏗️

*Created with ❤️ by [Mamun Hoque](https://github.com/MamunHoque)*
