# VS Code Sandbox - Enhanced Isolation Tool

🚀 **Complete VS Code isolation solution with global installation and self-update capabilities**

VS Code Sandbox creates completely isolated VS Code environments that simulate fresh OS installations with zero shared state between profiles or interference with your host system. Now enhanced with global installation, self-update, and project scaffolding features.

## ✨ Enhanced Features

- 🔒 **Complete Isolation** - Each profile runs in its own isolated environment
- 🏠 **Fresh OS Simulation** - Every profile behaves like a brand new operating system
- 🚫 **Zero Interference** - No impact on existing VS Code installations
- 🔄 **Multiple Profiles** - Unlimited isolated environments that coexist peacefully
- 🌐 **Global Installation** - Install once, use anywhere on your system
- 🔄 **Self-Updating** - Built-in update mechanism to get latest features
- 🗂️ **Advanced Management** - Create, launch, remove, and monitor profiles
- 📦 **Project Scaffolding** - Create project templates within isolated environments
- 🧪 **Well Tested** - Comprehensive test suite ensures isolation effectiveness
- 🛡️ **Safe Operations** - Non-destructive with clean removal capabilities

## 🚀 Quick Installation

### One-Line Installation (Recommended)
```bash
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash
```

### Manual Installation
```bash
# Download the installer
wget https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh

# Make it executable
chmod +x install-vscode-sandbox.sh

# Run the installer
sudo ./install-vscode-sandbox.sh
```

### Local Installation (No sudo required)
```bash
# Download the script
wget https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/vscode-sandbox

# Make it executable
chmod +x vscode-sandbox

# Use it locally
./vscode-sandbox myproject create
```

## 📋 Usage

### Basic Profile Management
```bash
# Create and launch isolated VS Code profile
vscode-sandbox myproject create

# Launch existing profile
vscode-sandbox myproject launch

# List all profiles
vscode-sandbox list

# Show profile status and information
vscode-sandbox myproject status

# Remove profile completely
vscode-sandbox myproject remove
```

### Project Scaffolding Within Profiles
```bash
# Create React project in isolated profile
vscode-sandbox myproject scaffold my-react-app --type react --git --vscode

# Create Node.js API in isolated profile
vscode-sandbox client-work scaffold api-server --type node --git --docker

# Create Python project in isolated profile
vscode-sandbox data-science scaffold ml-project --type python --git --vscode

# Create Go application in isolated profile
vscode-sandbox backend scaffold user-service --type go --git --docker

# Create static website in isolated profile
vscode-sandbox portfolio scaffold my-site --type static --git --vscode
```

### Global Commands
```bash
# Update to latest version
vscode-sandbox --update

# Show version information
vscode-sandbox --version

# Show help
vscode-sandbox --help

# Install globally (if not already installed)
sudo vscode-sandbox --install

# Uninstall global installation
sudo vscode-sandbox --uninstall
```

## 🎯 Supported Project Types

### Frontend & Web
- **react** - React application with modern setup
- **node** - Node.js application with Express
- **static** - Static HTML/CSS/JS website

### Backend & APIs
- **node** - Node.js server application
- **python** - Python project with virtual environment
- **go** - Go application with proper structure

### Configuration Options
- **--git** - Initialize Git repository with appropriate .gitignore
- **--vscode** - Add VS Code configuration (settings, extensions)
- **--docker** - Add Docker configuration (Dockerfile)

## 🔧 Advanced Usage

### Complete Development Environment Setup
```bash
# Create isolated profile for client work
vscode-sandbox client-alpha create

# Create multiple projects within the profile
vscode-sandbox client-alpha scaffold frontend --type react --git --vscode --docker
vscode-sandbox client-alpha scaffold backend --type node --git --vscode --docker
vscode-sandbox client-alpha scaffold docs --type static --git --vscode

# Launch the isolated environment
vscode-sandbox client-alpha launch
```

### Team Collaboration
```bash
# Create standardized development environments
vscode-sandbox team-frontend create
vscode-sandbox team-backend create
vscode-sandbox team-devops create

# Each team member gets identical, isolated environments
# No conflicts between different project requirements
```

### Technology Stack Isolation
```bash
# Separate environments for different tech stacks
vscode-sandbox python-ml create      # Machine learning projects
vscode-sandbox nodejs-web create     # Web development
vscode-sandbox go-microservices create  # Microservices development
```

## 📊 Profile Management

### Profile Status Information
```bash
vscode-sandbox myproject status
```

Shows:
- Basic profile information (name, paths, directories)
- Storage usage (total size, config, extensions, projects)
- Content summary (installed extensions, project count)
- Launcher availability and commands
- Last activity timestamp

### Profile Listing
```bash
vscode-sandbox list
```

Displays:
- All available isolated profiles
- Profile sizes and status
- Quick launch commands
- Management tips

## 🔄 Self-Update System

The VS Code Sandbox tool includes a built-in self-update mechanism:

```bash
# Check current version
vscode-sandbox --version

# Update to latest version
vscode-sandbox --update
```

The update process:
1. Downloads the latest version from GitHub
2. Validates the downloaded script
3. Backs up the current installation
4. Replaces with the new version
5. Verifies the installation

## 🌐 Global Installation Benefits

When installed globally, you can:
- Run `vscode-sandbox` from any directory
- Create isolated profiles anywhere on your system
- Use the self-update feature
- Access all features without path issues
- Manage profiles from any terminal session

## 🔧 System Requirements

### Minimum Requirements
- **Linux** operating system
- **Bash 4.0+**
- **util-linux** package (for `unshare` command)
- **VS Code** installed (any method: snap, deb, AppImage, etc.)
- **curl** or **wget** for downloading

### Optional Requirements
- **Git** (for --git flag in project scaffolding)
- **Docker** (for --docker flag in project scaffolding)
- **Node.js** (for React/Node.js project types)
- **Python 3** (for Python project types)
- **Go** (for Go project types)

## 🚨 Troubleshooting

### Permission Issues
```bash
# If you get permission errors during installation
sudo vscode-sandbox --install

# Or reinstall globally
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash
```

### Update Issues
```bash
# If update fails, try manual reinstallation
sudo vscode-sandbox --uninstall
curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash
```

### Command Not Found
```bash
# Check if /usr/local/bin is in your PATH
echo $PATH

# Add to PATH if missing (add to ~/.bashrc or ~/.zshrc)
export PATH="/usr/local/bin:$PATH"
```

### VS Code Not Found
```bash
# Set custom VS Code binary path
export VSCODE_BINARY="/path/to/your/code"
vscode-sandbox myproject create
```

### Namespace Issues
```bash
# Enable user namespaces if needed
echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone
```

## 💡 Tips and Best Practices

### Profile Naming
- Use descriptive names: `client-work`, `personal-dev`, `experimental`
- Avoid spaces and special characters
- Use hyphens or underscores for separation

### Project Organization
- Create profiles for different clients or projects
- Use project scaffolding to maintain consistency
- Keep related projects within the same profile

### Maintenance
- Regularly update the tool: `vscode-sandbox --update`
- Monitor profile sizes: `vscode-sandbox <profile> status`
- Clean up unused profiles: `vscode-sandbox <profile> remove`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Mamun Hoque**
- GitHub: [@MamunHoque](https://github.com/MamunHoque)
- Repository: [VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)

## 🔗 Links

- **Repository**: [https://github.com/MamunHoque/VSCodeSandbox](https://github.com/MamunHoque/VSCodeSandbox)
- **Issues**: [Report bugs or request features](https://github.com/MamunHoque/VSCodeSandbox/issues)
- **Discussions**: [Community discussions](https://github.com/MamunHoque/VSCodeSandbox/discussions)

---

**VS Code Sandbox** - Because every project deserves its own universe. 🌌

*Enhanced with global installation and self-update by [Mamun Hoque](https://github.com/MamunHoque)*
