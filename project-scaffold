#!/bin/bash

# VS Code Sandbox - Enhanced Isolation Tool
# Complete VS Code isolation solution with global installation and self-update
#
# Author: <PERSON><PERSON><PERSON>
# Repository: https://github.com/MamunHoque/VSCodeSandbox
# Version: 3.0.0

set -euo pipefail

# Script metadata
SCRIPT_VERSION="3.0.0"
SCRIPT_NAME="project-scaffold"
REPOSITORY_URL="https://github.com/MamunHoque/VSCodeSandbox"
REPOSITORY_RAW_URL="https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main"
INSTALL_PATH="/usr/local/bin/$SCRIPT_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ${NC} $1"; }
log_success() { echo -e "${GREEN}✅${NC} $1"; }
log_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
log_error() { echo -e "${RED}❌${NC} $1"; }
log_header() { echo -e "${PURPLE}🚀${NC} $1"; }
log_step() { echo -e "${CYAN}▶${NC} $1"; }

# Show banner
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    Project Scaffold v3.0                   ║
║              Universal Project Creation Tool                ║
║                                                              ║
║  🚀 Multi-Language Support  📦 Template System              ║
║  🔄 Self-Updating          🛠️ Customizable                  ║
║  🌐 Global Installation    📋 Best Practices                ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Show usage information
show_usage() {
    cat << EOF
${WHITE}USAGE:${NC}
    $SCRIPT_NAME <project-name> <project-type> [options]
    $SCRIPT_NAME [command] [options]

${WHITE}PROJECT TYPES:${NC}
    ${CYAN}react${NC}           - React application with Vite
    ${CYAN}react-ts${NC}        - React with TypeScript
    ${CYAN}nextjs${NC}          - Next.js application
    ${CYAN}vue${NC}             - Vue.js application
    ${CYAN}angular${NC}         - Angular application
    ${CYAN}node${NC}            - Node.js application
    ${CYAN}node-ts${NC}         - Node.js with TypeScript
    ${CYAN}express${NC}         - Express.js API server
    ${CYAN}fastify${NC}         - Fastify API server
    ${CYAN}python${NC}          - Python project with virtual environment
    ${CYAN}django${NC}          - Django web application
    ${CYAN}flask${NC}           - Flask web application
    ${CYAN}fastapi${NC}         - FastAPI application
    ${CYAN}go${NC}              - Go application
    ${CYAN}rust${NC}            - Rust application
    ${CYAN}java${NC}            - Java Maven project
    ${CYAN}spring${NC}          - Spring Boot application
    ${CYAN}php${NC}             - PHP project with Composer
    ${CYAN}laravel${NC}         - Laravel application
    ${CYAN}ruby${NC}            - Ruby project with Bundler
    ${CYAN}rails${NC}           - Ruby on Rails application
    ${CYAN}static${NC}          - Static HTML/CSS/JS website
    ${CYAN}electron${NC}        - Electron desktop application

${WHITE}COMMANDS:${NC}
    ${CYAN}--help, -h${NC}      - Show this help message
    ${CYAN}--version, -v${NC}   - Show version information
    ${CYAN}--list, -l${NC}      - List all available project types
    ${CYAN}--update${NC}        - Update to the latest version
    ${CYAN}--install${NC}       - Install globally to /usr/local/bin
    ${CYAN}--uninstall${NC}     - Remove global installation

${WHITE}OPTIONS:${NC}
    ${CYAN}--git${NC}           - Initialize Git repository
    ${CYAN}--vscode${NC}        - Add VS Code configuration
    ${CYAN}--docker${NC}        - Add Docker configuration
    ${CYAN}--github${NC}        - Add GitHub Actions workflow
    ${CYAN}--force${NC}         - Overwrite existing directory
    ${CYAN}--no-install${NC}    - Skip dependency installation
    ${CYAN}--template <url>${NC} - Use custom template from Git URL

${WHITE}EXAMPLES:${NC}
    $SCRIPT_NAME my-app react --git --vscode
    $SCRIPT_NAME api-server express --docker --github
    $SCRIPT_NAME ml-project python --git
    $SCRIPT_NAME my-site nextjs --vscode --docker
    $SCRIPT_NAME --update
    $SCRIPT_NAME --list

${WHITE}REPOSITORY:${NC} $REPOSITORY_URL
EOF
}

# Show version information
show_version() {
    echo -e "${WHITE}Project Scaffold${NC} version ${GREEN}$SCRIPT_VERSION${NC}"
    echo -e "Repository: ${BLUE}$REPOSITORY_URL${NC}"
    echo -e "Installation: ${CYAN}$INSTALL_PATH${NC}"
}

# List available project types
list_project_types() {
    log_header "Available Project Types"
    echo
    echo -e "${WHITE}Frontend Frameworks:${NC}"
    echo -e "  ${CYAN}react${NC}           - React application with Vite"
    echo -e "  ${CYAN}react-ts${NC}        - React with TypeScript"
    echo -e "  ${CYAN}nextjs${NC}          - Next.js application"
    echo -e "  ${CYAN}vue${NC}             - Vue.js application"
    echo -e "  ${CYAN}angular${NC}         - Angular application"
    echo -e "  ${CYAN}static${NC}          - Static HTML/CSS/JS website"
    echo
    echo -e "${WHITE}Backend & APIs:${NC}"
    echo -e "  ${CYAN}node${NC}            - Node.js application"
    echo -e "  ${CYAN}node-ts${NC}         - Node.js with TypeScript"
    echo -e "  ${CYAN}express${NC}         - Express.js API server"
    echo -e "  ${CYAN}fastify${NC}         - Fastify API server"
    echo -e "  ${CYAN}python${NC}          - Python project with virtual environment"
    echo -e "  ${CYAN}django${NC}          - Django web application"
    echo -e "  ${CYAN}flask${NC}           - Flask web application"
    echo -e "  ${CYAN}fastapi${NC}         - FastAPI application"
    echo
    echo -e "${WHITE}Systems & Compiled:${NC}"
    echo -e "  ${CYAN}go${NC}              - Go application"
    echo -e "  ${CYAN}rust${NC}            - Rust application"
    echo -e "  ${CYAN}java${NC}            - Java Maven project"
    echo -e "  ${CYAN}spring${NC}          - Spring Boot application"
    echo
    echo -e "${WHITE}Web Frameworks:${NC}"
    echo -e "  ${CYAN}php${NC}             - PHP project with Composer"
    echo -e "  ${CYAN}laravel${NC}         - Laravel application"
    echo -e "  ${CYAN}ruby${NC}            - Ruby project with Bundler"
    echo -e "  ${CYAN}rails${NC}           - Ruby on Rails application"
    echo
    echo -e "${WHITE}Desktop & Mobile:${NC}"
    echo -e "  ${CYAN}electron${NC}        - Electron desktop application"
    echo
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements for a project type
check_requirements() {
    local project_type="$1"
    
    case "$project_type" in
        "react"|"react-ts"|"vue"|"angular"|"node"|"node-ts"|"express"|"fastify"|"nextjs"|"electron"|"static")
            if ! command_exists "node"; then
                log_error "Node.js is required for $project_type projects"
                log_info "Install Node.js from: https://nodejs.org/"
                return 1
            fi
            ;;
        "python"|"django"|"flask"|"fastapi")
            if ! command_exists "python3"; then
                log_error "Python 3 is required for $project_type projects"
                log_info "Install Python from: https://python.org/"
                return 1
            fi
            ;;
        "go")
            if ! command_exists "go"; then
                log_error "Go is required for Go projects"
                log_info "Install Go from: https://golang.org/"
                return 1
            fi
            ;;
        "rust")
            if ! command_exists "cargo"; then
                log_error "Rust is required for Rust projects"
                log_info "Install Rust from: https://rustup.rs/"
                return 1
            fi
            ;;
        "java"|"spring")
            if ! command_exists "java" || ! command_exists "mvn"; then
                log_error "Java and Maven are required for Java projects"
                log_info "Install Java and Maven"
                return 1
            fi
            ;;
        "php"|"laravel")
            if ! command_exists "php"; then
                log_error "PHP is required for PHP projects"
                log_info "Install PHP from your package manager"
                return 1
            fi
            ;;
        "ruby"|"rails")
            if ! command_exists "ruby"; then
                log_error "Ruby is required for Ruby projects"
                log_info "Install Ruby from: https://ruby-lang.org/"
                return 1
            fi
            ;;
    esac
    
    return 0
}

# Parse command line arguments
parse_arguments() {
    PROJECT_NAME=""
    PROJECT_TYPE=""
    ENABLE_GIT=false
    ENABLE_VSCODE=false
    ENABLE_DOCKER=false
    ENABLE_GITHUB=false
    FORCE_OVERWRITE=false
    SKIP_INSTALL=false
    CUSTOM_TEMPLATE=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            --version|-v)
                show_version
                exit 0
                ;;
            --list|-l)
                list_project_types
                exit 0
                ;;
            --update)
                self_update
                exit 0
                ;;
            --install)
                install_globally
                exit 0
                ;;
            --uninstall)
                uninstall_globally
                exit 0
                ;;
            --git)
                ENABLE_GIT=true
                shift
                ;;
            --vscode)
                ENABLE_VSCODE=true
                shift
                ;;
            --docker)
                ENABLE_DOCKER=true
                shift
                ;;
            --github)
                ENABLE_GITHUB=true
                shift
                ;;
            --force)
                FORCE_OVERWRITE=true
                shift
                ;;
            --no-install)
                SKIP_INSTALL=true
                shift
                ;;
            --template)
                CUSTOM_TEMPLATE="$2"
                shift 2
                ;;
            -*)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$PROJECT_NAME" ]]; then
                    PROJECT_NAME="$1"
                elif [[ -z "$PROJECT_TYPE" ]]; then
                    PROJECT_TYPE="$1"
                else
                    log_error "Too many arguments: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# Self-update functionality
self_update() {
    log_header "Updating Project Scaffold"

    # Check if we have write permissions
    if [[ ! -w "$INSTALL_PATH" ]] && [[ -f "$INSTALL_PATH" ]]; then
        log_error "No write permission to $INSTALL_PATH"
        log_info "Try running with sudo: sudo $SCRIPT_NAME --update"
        exit 1
    fi

    # Create temporary file
    local temp_file=$(mktemp)

    # Download latest version
    log_step "Downloading latest version..."
    if command_exists "curl"; then
        if curl -sSL "$REPOSITORY_RAW_URL/project-scaffold" -o "$temp_file"; then
            log_success "Downloaded latest version"
        else
            log_error "Failed to download update"
            rm -f "$temp_file"
            exit 1
        fi
    elif command_exists "wget"; then
        if wget -q "$REPOSITORY_RAW_URL/project-scaffold" -O "$temp_file"; then
            log_success "Downloaded latest version"
        else
            log_error "Failed to download update"
            rm -f "$temp_file"
            exit 1
        fi
    else
        log_error "Neither curl nor wget available for downloading"
        rm -f "$temp_file"
        exit 1
    fi

    # Verify the downloaded file
    if [[ ! -s "$temp_file" ]]; then
        log_error "Downloaded file is empty"
        rm -f "$temp_file"
        exit 1
    fi

    # Check if it's a valid script
    if ! head -1 "$temp_file" | grep -q "#!/bin/bash"; then
        log_error "Downloaded file is not a valid bash script"
        rm -f "$temp_file"
        exit 1
    fi

    # Backup current version if it exists
    if [[ -f "$INSTALL_PATH" ]]; then
        log_step "Backing up current version..."
        cp "$INSTALL_PATH" "${INSTALL_PATH}.backup"
    fi

    # Install new version
    log_step "Installing new version..."
    if [[ -f "$INSTALL_PATH" ]]; then
        # Update existing installation
        cp "$temp_file" "$INSTALL_PATH"
        chmod +x "$INSTALL_PATH"
    else
        # Install to current location if not globally installed
        cp "$temp_file" "$0"
        chmod +x "$0"
    fi

    # Clean up
    rm -f "$temp_file"

    log_success "Update completed successfully!"
    log_info "Restart your terminal or run the command again to use the new version"
}

# Install globally to /usr/local/bin
install_globally() {
    log_header "Installing Project Scaffold Globally"

    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        log_error "Global installation requires root privileges"
        log_info "Run with sudo: sudo $0 --install"
        exit 1
    fi

    # Create /usr/local/bin if it doesn't exist
    mkdir -p /usr/local/bin

    # Copy script to global location
    log_step "Installing to $INSTALL_PATH..."
    cp "$0" "$INSTALL_PATH"
    chmod +x "$INSTALL_PATH"

    # Verify installation
    if [[ -x "$INSTALL_PATH" ]]; then
        log_success "Successfully installed to $INSTALL_PATH"
        log_info "You can now run 'project-scaffold' from anywhere"

        # Show version to confirm
        "$INSTALL_PATH" --version
    else
        log_error "Installation failed"
        exit 1
    fi
}

# Uninstall global installation
uninstall_globally() {
    log_header "Uninstalling Project Scaffold"

    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        log_error "Global uninstallation requires root privileges"
        log_info "Run with sudo: sudo $0 --uninstall"
        exit 1
    fi

    if [[ -f "$INSTALL_PATH" ]]; then
        log_step "Removing $INSTALL_PATH..."
        rm -f "$INSTALL_PATH"
        log_success "Project Scaffold uninstalled successfully"
    else
        log_warning "Project Scaffold is not globally installed"
    fi
}

# Validate project name
validate_project_name() {
    local name="$1"

    if [[ -z "$name" ]]; then
        log_error "Project name cannot be empty"
        return 1
    fi

    # Check for valid characters (alphanumeric, hyphens, underscores)
    if [[ ! "$name" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        log_error "Project name can only contain letters, numbers, hyphens, and underscores"
        return 1
    fi

    # Check if directory already exists
    if [[ -d "$name" ]] && [[ "$FORCE_OVERWRITE" != true ]]; then
        log_error "Directory '$name' already exists"
        log_info "Use --force to overwrite or choose a different name"
        return 1
    fi

    return 0
}

# Validate project type
validate_project_type() {
    local type="$1"
    local valid_types=(
        "react" "react-ts" "nextjs" "vue" "angular" "static" "electron"
        "node" "node-ts" "express" "fastify"
        "python" "django" "flask" "fastapi"
        "go" "rust" "java" "spring"
        "php" "laravel" "ruby" "rails"
    )

    for valid_type in "${valid_types[@]}"; do
        if [[ "$type" == "$valid_type" ]]; then
            return 0
        fi
    done

    log_error "Invalid project type: $type"
    log_info "Run '$SCRIPT_NAME --list' to see available project types"
    return 1
}

# Initialize Git repository
init_git() {
    local project_dir="$1"

    if [[ "$ENABLE_GIT" == true ]]; then
        log_step "Initializing Git repository..."
        cd "$project_dir"

        if command_exists "git"; then
            git init

            # Create .gitignore based on project type
            create_gitignore "$PROJECT_TYPE"

            # Initial commit
            git add .
            git commit -m "Initial commit: $PROJECT_TYPE project created with project-scaffold"

            log_success "Git repository initialized"
        else
            log_warning "Git not found, skipping Git initialization"
        fi

        cd ..
    fi
}

# Create .gitignore file
create_gitignore() {
    local project_type="$1"

    cat > .gitignore << 'EOF'
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

EOF

    case "$project_type" in
        "react"|"react-ts"|"vue"|"angular"|"node"|"node-ts"|"express"|"fastify"|"nextjs"|"electron"|"static")
            cat >> .gitignore << 'EOF'
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Build outputs
dist/
build/
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

EOF
            ;;
        "python"|"django"|"flask"|"fastapi")
            cat >> .gitignore << 'EOF'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/

# Django
*.log
local_settings.py
db.sqlite3
media/

# Flask
instance/
.webassets-cache

EOF
            ;;
        "go")
            cat >> .gitignore << 'EOF'
# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

EOF
            ;;
        "rust")
            cat >> .gitignore << 'EOF'
# Rust
/target/
Cargo.lock

EOF
            ;;
        "java"|"spring")
            cat >> .gitignore << 'EOF'
# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

EOF
            ;;
        "php"|"laravel")
            cat >> .gitignore << 'EOF'
# PHP
/vendor/
composer.phar
composer.lock
.env

# Laravel
/bootstrap/compiled.php
/app/storage/
/public/storage
/storage/*.key

EOF
            ;;
        "ruby"|"rails")
            cat >> .gitignore << 'EOF'
# Ruby
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# Rails
/log/*
/db/*.sqlite3
/db/*.sqlite3-journal
/public/system
/coverage/
/spec/tmp
**.orig
rerun.txt
pickle-email-*.html

EOF
            ;;
    esac
}

# Add VS Code configuration
add_vscode_config() {
    local project_dir="$1"

    if [[ "$ENABLE_VSCODE" == true ]]; then
        log_step "Adding VS Code configuration..."

        mkdir -p "$project_dir/.vscode"

        # Create settings.json
        cat > "$project_dir/.vscode/settings.json" << 'EOF'
{
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": true
    },
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true
}
EOF

        # Create extensions.json with recommended extensions
        create_vscode_extensions "$PROJECT_TYPE" "$project_dir"

        # Create launch.json for debugging
        create_vscode_launch "$PROJECT_TYPE" "$project_dir"

        log_success "VS Code configuration added"
    fi
}

# Create VS Code extensions recommendations
create_vscode_extensions() {
    local project_type="$1"
    local project_dir="$2"

    local extensions=()

    # Common extensions
    extensions+=("ms-vscode.vscode-json")
    extensions+=("editorconfig.editorconfig")
    extensions+=("ms-vscode.vscode-typescript-next")

    case "$project_type" in
        "react"|"react-ts"|"nextjs")
            extensions+=("bradlc.vscode-tailwindcss")
            extensions+=("es7-react-js-snippets")
            extensions+=("ms-vscode.vscode-typescript-next")
            ;;
        "vue")
            extensions+=("vue.volar")
            extensions+=("vue.vscode-typescript-vue-plugin")
            ;;
        "angular")
            extensions+=("angular.ng-template")
            extensions+=("ms-vscode.vscode-typescript-next")
            ;;
        "python"|"django"|"flask"|"fastapi")
            extensions+=("ms-python.python")
            extensions+=("ms-python.flake8")
            extensions+=("ms-python.black-formatter")
            ;;
        "go")
            extensions+=("golang.go")
            ;;
        "rust")
            extensions+=("rust-lang.rust-analyzer")
            ;;
        "java"|"spring")
            extensions+=("redhat.java")
            extensions+=("vscjava.vscode-java-pack")
            ;;
        "php"|"laravel")
            extensions+=("bmewburn.vscode-intelephense-client")
            extensions+=("onecentlin.laravel-blade")
            ;;
        "ruby"|"rails")
            extensions+=("rebornix.ruby")
            extensions+=("kaiwood.endwise")
            ;;
    esac

    # Create extensions.json
    cat > "$project_dir/.vscode/extensions.json" << EOF
{
    "recommendations": [
$(printf '        "%s",\n' "${extensions[@]}" | sed '$ s/,$//')
    ]
}
EOF
}

# Create VS Code launch configuration
create_vscode_launch() {
    local project_type="$1"
    local project_dir="$2"

    case "$project_type" in
        "node"|"node-ts"|"express"|"fastify")
            cat > "$project_dir/.vscode/launch.json" << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Program",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/src/index.js",
            "console": "integratedTerminal",
            "env": {
                "NODE_ENV": "development"
            }
        }
    ]
}
EOF
            ;;
        "python"|"django"|"flask"|"fastapi")
            cat > "$project_dir/.vscode/launch.json" << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        }
    ]
}
EOF
            ;;
        "go")
            cat > "$project_dir/.vscode/launch.json" << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Package",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}"
        }
    ]
}
EOF
            ;;
    esac
}

# Add Docker configuration
add_docker_config() {
    local project_dir="$1"

    if [[ "$ENABLE_DOCKER" == true ]]; then
        log_step "Adding Docker configuration..."

        create_dockerfile "$PROJECT_TYPE" "$project_dir"
        create_docker_compose "$PROJECT_TYPE" "$project_dir"
        create_dockerignore "$project_dir"

        log_success "Docker configuration added"
    fi
}

# Create Dockerfile
create_dockerfile() {
    local project_type="$1"
    local project_dir="$2"

    case "$project_type" in
        "react"|"react-ts"|"vue"|"angular"|"nextjs"|"static")
            cat > "$project_dir/Dockerfile" << 'EOF'
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY --from=builder /app/nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF
            ;;
        "node"|"node-ts"|"express"|"fastify")
            cat > "$project_dir/Dockerfile" << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 3000

CMD ["npm", "start"]
EOF
            ;;
        "python"|"django"|"flask"|"fastapi")
            cat > "$project_dir/Dockerfile" << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["python", "app.py"]
EOF
            ;;
        "go")
            cat > "$project_dir/Dockerfile" << 'EOF'
# Build stage
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main .

# Production stage
FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .

EXPOSE 8080
CMD ["./main"]
EOF
            ;;
    esac
}

# Create docker-compose.yml
create_docker_compose() {
    local project_type="$1"
    local project_dir="$2"

    case "$project_type" in
        "node"|"node-ts"|"express"|"fastify"|"react"|"react-ts"|"vue"|"angular"|"nextjs")
            cat > "$project_dir/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
EOF
            ;;
        "python"|"django"|"flask"|"fastapi")
            cat > "$project_dir/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
    volumes:
      - .:/app
    command: python app.py

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
EOF
            ;;
        "go")
            cat > "$project_dir/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - .:/app
    working_dir: /app
    command: go run main.go
EOF
            ;;
    esac
}

# Create .dockerignore
create_dockerignore() {
    local project_dir="$1"

    cat > "$project_dir/.dockerignore" << 'EOF'
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
.vscode
.idea
*.swp
*.swo
*~
EOF
}

# Add GitHub Actions workflow
add_github_actions() {
    local project_dir="$1"

    if [[ "$ENABLE_GITHUB" == true ]]; then
        log_step "Adding GitHub Actions workflow..."

        mkdir -p "$project_dir/.github/workflows"
        create_github_workflow "$PROJECT_TYPE" "$project_dir"

        log_success "GitHub Actions workflow added"
    fi
}

# Create GitHub Actions workflow
create_github_workflow() {
    local project_type="$1"
    local project_dir="$2"

    case "$project_type" in
        "react"|"react-ts"|"vue"|"angular"|"node"|"node-ts"|"express"|"fastify"|"nextjs"|"electron")
            cat > "$project_dir/.github/workflows/ci.yml" << 'EOF'
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - run: npm ci
    - run: npm run build --if-present
    - run: npm test
EOF
            ;;
        "python"|"django"|"flask"|"fastapi")
            cat > "$project_dir/.github/workflows/ci.yml" << 'EOF'
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest flake8

    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Test with pytest
      run: |
        pytest
EOF
            ;;
        "go")
            cat > "$project_dir/.github/workflows/ci.yml" << 'EOF'
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21

    - name: Build
      run: go build -v ./...

    - name: Test
      run: go test -v ./...
EOF
            ;;
    esac
}

# Create project based on type
create_project() {
    local project_name="$1"
    local project_type="$2"

    log_header "Creating $project_type project: $project_name"

    # Remove existing directory if force is enabled
    if [[ -d "$project_name" ]] && [[ "$FORCE_OVERWRITE" == true ]]; then
        log_warning "Removing existing directory: $project_name"
        rm -rf "$project_name"
    fi

    # Create project directory
    mkdir -p "$project_name"
    cd "$project_name"

    # Create project based on type
    case "$project_type" in
        "react")
            create_react_project "$project_name"
            ;;
        "react-ts")
            create_react_ts_project "$project_name"
            ;;
        "nextjs")
            create_nextjs_project "$project_name"
            ;;
        "vue")
            create_vue_project "$project_name"
            ;;
        "angular")
            create_angular_project "$project_name"
            ;;
        "node")
            create_node_project "$project_name"
            ;;
        "node-ts")
            create_node_ts_project "$project_name"
            ;;
        "express")
            create_express_project "$project_name"
            ;;
        "fastify")
            create_fastify_project "$project_name"
            ;;
        "python")
            create_python_project "$project_name"
            ;;
        "django")
            create_django_project "$project_name"
            ;;
        "flask")
            create_flask_project "$project_name"
            ;;
        "fastapi")
            create_fastapi_project "$project_name"
            ;;
        "go")
            create_go_project "$project_name"
            ;;
        "rust")
            create_rust_project "$project_name"
            ;;
        "java")
            create_java_project "$project_name"
            ;;
        "spring")
            create_spring_project "$project_name"
            ;;
        "php")
            create_php_project "$project_name"
            ;;
        "laravel")
            create_laravel_project "$project_name"
            ;;
        "ruby")
            create_ruby_project "$project_name"
            ;;
        "rails")
            create_rails_project "$project_name"
            ;;
        "static")
            create_static_project "$project_name"
            ;;
        "electron")
            create_electron_project "$project_name"
            ;;
        *)
            log_error "Unsupported project type: $project_type"
            cd ..
            rmdir "$project_name" 2>/dev/null || true
            exit 1
            ;;
    esac

    cd ..

    # Add additional configurations
    add_vscode_config "$project_name"
    add_docker_config "$project_name"
    add_github_actions "$project_name"
    init_git "$project_name"

    log_success "Project '$project_name' created successfully!"
    log_info "Next steps:"
    echo -e "  ${CYAN}cd $project_name${NC}"

    # Show project-specific next steps
    show_next_steps "$project_type"
}

# Show next steps based on project type
show_next_steps() {
    local project_type="$1"

    case "$project_type" in
        "react"|"react-ts"|"vue"|"angular"|"nextjs"|"electron"|"static")
            echo -e "  ${CYAN}npm install${NC}     # Install dependencies"
            echo -e "  ${CYAN}npm run dev${NC}     # Start development server"
            ;;
        "node"|"node-ts"|"express"|"fastify")
            echo -e "  ${CYAN}npm install${NC}     # Install dependencies"
            echo -e "  ${CYAN}npm start${NC}       # Start the application"
            echo -e "  ${CYAN}npm run dev${NC}     # Start with hot reload"
            ;;
        "python"|"django"|"flask"|"fastapi")
            echo -e "  ${CYAN}python -m venv venv${NC}              # Create virtual environment"
            echo -e "  ${CYAN}source venv/bin/activate${NC}         # Activate virtual environment"
            echo -e "  ${CYAN}pip install -r requirements.txt${NC}  # Install dependencies"
            echo -e "  ${CYAN}python app.py${NC}                    # Run the application"
            ;;
        "go")
            echo -e "  ${CYAN}go mod tidy${NC}     # Download dependencies"
            echo -e "  ${CYAN}go run main.go${NC}  # Run the application"
            ;;
        "rust")
            echo -e "  ${CYAN}cargo build${NC}     # Build the project"
            echo -e "  ${CYAN}cargo run${NC}       # Run the application"
            ;;
        "java"|"spring")
            echo -e "  ${CYAN}mvn clean install${NC}  # Build the project"
            echo -e "  ${CYAN}mvn spring-boot:run${NC} # Run the application (Spring Boot)"
            echo -e "  ${CYAN}java -jar target/*.jar${NC} # Run the JAR file"
            ;;
        "php"|"laravel")
            echo -e "  ${CYAN}composer install${NC}    # Install dependencies"
            echo -e "  ${CYAN}php -S localhost:8000${NC} # Start development server"
            ;;
        "ruby"|"rails")
            echo -e "  ${CYAN}bundle install${NC}  # Install dependencies"
            echo -e "  ${CYAN}rails server${NC}    # Start Rails server (Rails projects)"
            echo -e "  ${CYAN}ruby app.rb${NC}     # Run Ruby application"
            ;;
    esac

    if [[ "$ENABLE_DOCKER" == true ]]; then
        echo -e "  ${CYAN}docker-compose up${NC}  # Run with Docker"
    fi
}

# Create React project
create_react_project() {
    local project_name="$1"

    log_step "Creating React project with Vite..."

    if [[ "$SKIP_INSTALL" != true ]]; then
        npm create vite@latest . -- --template react
        log_success "React project created"
    else
        # Create minimal structure
        create_package_json_react "$project_name"
        create_react_structure
    fi
}

# Create React TypeScript project
create_react_ts_project() {
    local project_name="$1"

    log_step "Creating React TypeScript project with Vite..."

    if [[ "$SKIP_INSTALL" != true ]]; then
        npm create vite@latest . -- --template react-ts
        log_success "React TypeScript project created"
    else
        create_package_json_react_ts "$project_name"
        create_react_ts_structure
    fi
}

# Create Next.js project
create_nextjs_project() {
    local project_name="$1"

    log_step "Creating Next.js project..."

    if [[ "$SKIP_INSTALL" != true ]]; then
        npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
        log_success "Next.js project created"
    else
        create_package_json_nextjs "$project_name"
        create_nextjs_structure
    fi
}

# Create Node.js project
create_node_project() {
    local project_name="$1"

    log_step "Creating Node.js project..."

    # Initialize npm project
    npm init -y

    # Update package.json
    create_package_json_node "$project_name"

    # Create project structure
    mkdir -p src

    # Create main application file
    cat > src/index.js << 'EOF'
const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'Hello World!',
        timestamp: new Date().toISOString()
    });
});

app.get('/health', (req, res) => {
    res.json({ status: 'OK', uptime: process.uptime() });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

module.exports = app;
EOF

    # Create test file
    mkdir -p tests
    cat > tests/app.test.js << 'EOF'
const request = require('supertest');
const app = require('../src/index');

describe('GET /', () => {
    it('should return hello world message', async () => {
        const res = await request(app).get('/');
        expect(res.statusCode).toBe(200);
        expect(res.body).toHaveProperty('message', 'Hello World!');
    });
});

describe('GET /health', () => {
    it('should return health status', async () => {
        const res = await request(app).get('/health');
        expect(res.statusCode).toBe(200);
        expect(res.body).toHaveProperty('status', 'OK');
    });
});
EOF

    if [[ "$SKIP_INSTALL" != true ]]; then
        npm install express
        npm install --save-dev jest supertest nodemon
    fi

    log_success "Node.js project created"
}

# Create Python project
create_python_project() {
    local project_name="$1"

    log_step "Creating Python project..."

    # Create project structure
    mkdir -p src tests docs

    # Create main application file
    cat > src/main.py << 'EOF'
#!/usr/bin/env python3
"""
Main application module
"""

import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main application function"""
    logger.info("Starting application...")

    print(f"Hello World! Current time: {datetime.now()}")

    logger.info("Application completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
EOF

    # Create requirements.txt
    cat > requirements.txt << 'EOF'
# Core dependencies
requests>=2.31.0
python-dotenv>=1.0.0

# Development dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
EOF

    # Create setup.py
    cat > setup.py << EOF
from setuptools import setup, find_packages

setup(
    name="$project_name",
    version="0.1.0",
    description="A Python project created with project-scaffold",
    packages=find_packages(),
    python_requires=">=3.8",
    install_requires=[
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "$project_name=src.main:main",
        ],
    },
)
EOF

    # Create test file
    cat > tests/test_main.py << 'EOF'
import pytest
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import main

def test_main():
    """Test main function"""
    result = main()
    assert result == 0
EOF

    # Create pytest configuration
    cat > pytest.ini << 'EOF'
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = --verbose --cov=src --cov-report=html --cov-report=term-missing
EOF

    # Create .env file
    cat > .env.example << 'EOF'
# Environment variables
DEBUG=True
LOG_LEVEL=INFO
EOF

    log_success "Python project created"
}

# Create package.json for Node.js projects
create_package_json_node() {
    local project_name="$1"

    cat > package.json << EOF
{
  "name": "$project_name",
  "version": "1.0.0",
  "description": "A Node.js project created with project-scaffold",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  },
  "keywords": ["nodejs", "express"],
  "author": "",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "nodemon": "^3.0.1",
    "supertest": "^6.3.3"
  }
}
EOF
}

# Create Express.js project
create_express_project() {
    local project_name="$1"

    log_step "Creating Express.js project..."

    # Initialize npm project
    npm init -y

    # Update package.json for Express
    cat > package.json << EOF
{
  "name": "$project_name",
  "version": "1.0.0",
  "description": "An Express.js API server created with project-scaffold",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  },
  "keywords": ["express", "api", "nodejs"],
  "author": "",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "nodemon": "^3.0.1",
    "supertest": "^6.3.3"
  }
}
EOF

    # Create project structure
    mkdir -p src/{routes,middleware,controllers,models} tests

    # Create main app file
    cat > src/app.js << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'Express API Server',
        version: '1.0.0',
        timestamp: new Date().toISOString()
    });
});

app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// API routes
app.use('/api', require('./routes'));

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

// Start server
if (require.main === module) {
    app.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
    });
}

module.exports = app;
EOF

    # Create routes
    cat > src/routes/index.js << 'EOF'
const express = require('express');
const router = express.Router();

// Example route
router.get('/users', (req, res) => {
    res.json({ users: [] });
});

router.post('/users', (req, res) => {
    const { name, email } = req.body;
    res.status(201).json({ id: 1, name, email });
});

module.exports = router;
EOF

    # Create environment file
    cat > .env.example << 'EOF'
PORT=3000
NODE_ENV=development
DATABASE_URL=
JWT_SECRET=your-secret-key
EOF

    if [[ "$SKIP_INSTALL" != true ]]; then
        npm install
    fi

    log_success "Express.js project created"
}

# Create Go project
create_go_project() {
    local project_name="$1"

    log_step "Creating Go project..."

    # Initialize Go module
    go mod init "$project_name"

    # Create project structure
    mkdir -p cmd internal/{handlers,models,services} pkg

    # Create main.go
    cat > cmd/main.go << 'EOF'
package main

import (
    "fmt"
    "log"
    "net/http"
    "os"
)

func main() {
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }

    http.HandleFunc("/", homeHandler)
    http.HandleFunc("/health", healthHandler)

    fmt.Printf("Server starting on port %s\n", port)
    log.Fatal(http.ListenAndServe(":"+port, nil))
}

func homeHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    fmt.Fprintf(w, `{"message": "Hello from Go!", "version": "1.0.0"}`)
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    fmt.Fprintf(w, `{"status": "OK"}`)
}
EOF

    # Create go.mod if not exists
    if [[ ! -f "go.mod" ]]; then
        go mod init "$project_name"
    fi

    # Create README
    cat > README.md << EOF
# $project_name

A Go application created with project-scaffold.

## Getting Started

\`\`\`bash
# Build the application
go build -o bin/$project_name cmd/main.go

# Run the application
go run cmd/main.go

# Test the application
go test ./...
\`\`\`

## API Endpoints

- \`GET /\` - Home endpoint
- \`GET /health\` - Health check
EOF

    if [[ "$SKIP_INSTALL" != true ]]; then
        go mod tidy
    fi

    log_success "Go project created"
}

# Create static website project
create_static_project() {
    local project_name="$1"

    log_step "Creating static website project..."

    # Create project structure
    mkdir -p src/{css,js,images} dist

    # Create index.html
    cat > src/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$project_name</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <nav>
            <h1>$project_name</h1>
            <ul>
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="home">
            <h2>Welcome to $project_name</h2>
            <p>This is a static website created with project-scaffold.</p>
        </section>

        <section id="about">
            <h2>About</h2>
            <p>This project was generated using the project-scaffold tool.</p>
        </section>

        <section id="contact">
            <h2>Contact</h2>
            <p>Get in touch with us!</p>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 $project_name. All rights reserved.</p>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
EOF

    # Create CSS
    cat > src/css/style.css << 'EOF'
/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Header and navigation */
header {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

nav h1 {
    font-size: 1.5rem;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

nav a:hover {
    color: #3498db;
}

/* Main content */
main {
    margin-top: 80px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 2rem;
}

section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
}

h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

/* Footer */
footer {
    background: #34495e;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 3rem;
}

/* Responsive design */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        gap: 1rem;
    }

    nav ul {
        gap: 1rem;
    }

    main {
        margin-top: 120px;
        padding: 1rem;
    }
}
EOF

    # Create JavaScript
    cat > src/js/script.js << 'EOF'
// Simple smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Simple fade-in animation for sections
    const sections = document.querySelectorAll('section');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
});
EOF

    # Create package.json for build tools (optional)
    cat > package.json << EOF
{
  "name": "$project_name",
  "version": "1.0.0",
  "description": "A static website created with project-scaffold",
  "scripts": {
    "build": "cp -r src/* dist/",
    "serve": "python3 -m http.server 8000 --directory src",
    "dev": "python3 -m http.server 8000 --directory src"
  },
  "keywords": ["static", "website", "html", "css", "javascript"],
  "author": "",
  "license": "MIT"
}
EOF

    log_success "Static website project created"
}

# Placeholder functions for remaining project types
create_vue_project() { log_warning "Vue project creation not yet implemented"; }
create_angular_project() { log_warning "Angular project creation not yet implemented"; }
create_node_ts_project() { log_warning "Node.js TypeScript project creation not yet implemented"; }
create_fastify_project() { log_warning "Fastify project creation not yet implemented"; }
create_django_project() { log_warning "Django project creation not yet implemented"; }
create_flask_project() { log_warning "Flask project creation not yet implemented"; }
create_fastapi_project() { log_warning "FastAPI project creation not yet implemented"; }
create_rust_project() { log_warning "Rust project creation not yet implemented"; }
create_java_project() { log_warning "Java project creation not yet implemented"; }
create_spring_project() { log_warning "Spring Boot project creation not yet implemented"; }
create_php_project() { log_warning "PHP project creation not yet implemented"; }
create_laravel_project() { log_warning "Laravel project creation not yet implemented"; }
create_ruby_project() { log_warning "Ruby project creation not yet implemented"; }
create_rails_project() { log_warning "Rails project creation not yet implemented"; }
create_electron_project() { log_warning "Electron project creation not yet implemented"; }

# Additional placeholder functions for React structure creation
create_package_json_react() { log_warning "React package.json creation not yet implemented"; }
create_react_structure() { log_warning "React structure creation not yet implemented"; }
create_package_json_react_ts() { log_warning "React TypeScript package.json creation not yet implemented"; }
create_react_ts_structure() { log_warning "React TypeScript structure creation not yet implemented"; }
create_package_json_nextjs() { log_warning "Next.js package.json creation not yet implemented"; }
create_nextjs_structure() { log_warning "Next.js structure creation not yet implemented"; }

# Main function
main() {
    # Parse command line arguments
    parse_arguments "$@"

    # If no arguments provided, show usage
    if [[ $# -eq 0 ]]; then
        show_banner
        show_usage
        exit 0
    fi

    # Validate inputs
    if [[ -z "$PROJECT_NAME" ]] || [[ -z "$PROJECT_TYPE" ]]; then
        log_error "Both project name and project type are required"
        show_usage
        exit 1
    fi

    # Validate project name
    if ! validate_project_name "$PROJECT_NAME"; then
        exit 1
    fi

    # Validate project type
    if ! validate_project_type "$PROJECT_TYPE"; then
        exit 1
    fi

    # Check system requirements
    if ! check_requirements "$PROJECT_TYPE"; then
        exit 1
    fi

    # Create the project
    create_project "$PROJECT_NAME" "$PROJECT_TYPE"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
